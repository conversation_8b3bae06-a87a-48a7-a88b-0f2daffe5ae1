import { <PERSON><PERSON><PERSON>, MiddlewareConsumer, Logger } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './domains/users/users.module';
import { ConfigModule } from '@nestjs/config';
import { AuthMiddleware } from './common/middleware/auth.middleware';
import { AlertZoneController } from './domains/alertZones/alertZone.controller';
import { AlertZoneModule } from './domains/alertZones/alertZone.module';
import { LoggingModule } from './domains/logging/logging.module';
import { LoggingController } from './domains/logging/logging.controller';
import { NotificationModule } from './domains/alertNotifications/notification.module';
import { ServiceZoneModule } from './domains/serviceZone/serviceZone.module';
import { PythonExecutionController } from './domains/pythonExecution/pythonExecution.controller';
import { PythonExecutionModule } from './domains/pythonExecution/pythonExecution.module';
import { UserPreferencesController } from './domains/userPreferences/userPreferences.controller';
import { ServiceZonesController } from './domains/serviceZone/serviceZone.controller';
import { RolesController } from './domains/roles/roles.controller';
import { H3Service } from './utils/h3.service';
import { SystemNotificationsController } from './domains/systemNotifications/systemNotification.controller';
import { SystemNotificationModule } from './domains/systemNotifications/systemNotification.module';
import { DroneModule } from './domains/drones/drone.module';
import { DroneController } from './domains/drones/drone.controller';
import { DroneAuthorizationModule } from './domains/droneAuthorizations/droneAuthorization.module';
import { DroneAuthorizationController } from './domains/droneAuthorizations/droneAuthorization.controller';
import { AdminModule } from './domains/admin/admin.module';
import { AdminController } from './domains/admin/admin.controller';
import { EventModule } from './domains/events/event.module';
import { DashboardModule } from './domains/dashboard/dashboard.module';
import { DashboardController } from './domains/dashboard/dashboard.controller';
import { CacheModule } from '@nestjs/cache-manager';
import { createKeyv } from '@keyv/redis';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { HttpCacheInterceptor } from './common/http-cache.interceptor';

@Module({
  imports: [
    SystemNotificationModule,
    NotificationModule,
    UsersModule,
    AlertZoneModule,
    DatabaseModule,
    ServiceZoneModule,
    PythonExecutionModule,
    DroneModule,
    DroneAuthorizationModule,
    AdminModule,
    EventModule,
    DashboardModule,
    LoggingModule,
    ConfigModule.forRoot(),
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: async () => {
        return {
          stores: [
            createKeyv({
              url: process.env.REDIS_HOST || "redis://localhost:6379",
            }),
          ],
        };
      },
    }),
  ],
 controllers: [
    AppController,
    AlertZoneController,
    PythonExecutionController,
    UserPreferencesController,
    ServiceZonesController,
    RolesController,
    SystemNotificationsController,
    DroneController,
    DroneAuthorizationController,
    AdminController,
    DashboardController,
    LoggingController
  ],
  providers: [AppService, H3Service, Logger
  //   {
  //   provide: APP_INTERCEPTOR,
  //   useClass: HttpCacheInterceptor,
  // },
],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).forRoutes('*');
  }
}
