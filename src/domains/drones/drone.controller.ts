import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Req
} from '@nestjs/common';
import { DroneService } from './drone.service';
import { Drone } from './drone.interface';
import { CreateDroneWithAuthorizationDto } from './dto/create-drone-with-authorization.dto';

@Controller('api/drones')
export class DroneController {
  constructor(private readonly droneService: DroneService) {}

  @Get()
  async findAuthorizedDrones(
    @Req() req: Request
  ): Promise<Drone[]> {
    const orgId = req['org_id']
    return this.droneService.findAuthorizedDrones(orgId);
  }

  @Get('by-device-id/:deviceId')
  async findByDeviceId(@Param('deviceId') deviceId: string): Promise<Drone> {
    return this.droneService.findByDeviceId(deviceId);
  }

  @Get('with-authorization/by-device-id/:deviceId')
  async findByDeviceIdWithAuthorization(@Param('deviceId') deviceId: string): Promise<any> {
    return this.droneService.getDroneWithAuthorizationByDeviceId(deviceId);
  }

  @Get('with-authorization/:id')
  async findOneWithAuthorization(@Param('id') id: string): Promise<any> {
    return this.droneService.getDroneWithAuthorization(id);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Drone> {
    return this.droneService.findOne(id);
  }

  @Post()
  async create(@Req() req: Request, @Body() droneData: Partial<Drone>): Promise<Drone> {
    const user = req['user'];
    droneData.org_id = user.org_id;

    return this.droneService.create(droneData, user._id);
  }

  @Post('with-authorization')
  async createWithAuthorization(
    @Req() req: Request,
    @Body() data: CreateDroneWithAuthorizationDto
  ): Promise<{ drone: Drone, authorization: any }> {
    const user = req['user'];
    
    data.drone.org_id = user.org_id;
    data.authorization.org_id = user.org_id;
    data.authorization.issued_at = new Date();
    data.authorization.authorized_by = user._id;

    return this.droneService.createOrUpdateDroneWithAuthorization(
      data.drone,
      data.authorization,
      user._id
    );
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() droneData: Partial<Drone>,
    @Req() req: Request
  ): Promise<Drone> {
    const user = req['user'];
    return this.droneService.update(id, droneData, user._id);
  }

  @Delete(':id')
  async delete(@Param('id') id: string, @Req() req: Request): Promise<{ message: string }> {
    const user = req['user'];
    return this.droneService.delete(id, user._id);
  }
}
