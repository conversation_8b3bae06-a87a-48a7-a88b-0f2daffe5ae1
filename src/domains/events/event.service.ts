import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EventProfile } from './eventProfile.interface';
import { EventHistory } from './eventHistory.interface';
import { EventProfileService } from './eventProfile.service';
import { EventHistoryService } from './eventHistory.service';
import { OrganizationService } from '../organizations/organization.service';
import Constants from 'src/common/constants';

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(
    @InjectModel(Constants.notification) private readonly notificationModel: Model<any>,
    private readonly eventProfileService: EventProfileService,
    private readonly eventHistoryService: EventHistoryService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Find all events with pagination and filtering (legacy method for compatibility)
   */
  async findForAlertZone(
    skip: number,
    limit: number,
    orgId: string,
    alertZoneId: string,
    search?: string,
    startDate?: Date,
    endDate?: Date,
    sort?: any,
    sortDirection?: string
  ) {
    const sortObj = {};
    if (sort) {
      sortObj[sort] = sortDirection === 'desc' ? -1 : 1;
    }

    const objectIdOrgId = await this.organizationService.findByAuth0Id(orgId);
    this.logger.log(
      `Filters: startDate ${startDate?.toISOString()} EndDate: ${endDate?.toISOString()} search: ${search}`,
      `auth0_id: ${orgId}`,
      `actual org id: ${objectIdOrgId._id.toString()}`
    );

    const events = await this.notificationModel
      .aggregate([
        {
          $match: {
            org_id: objectIdOrgId._id,
          },
        },
        {
          $match: {
            'timestamp': {
              $gte: startDate,
              $lte: endDate,
            },
            'alertZone._id': alertZoneId,
          },
        },
        {
          $lookup: {
            from: Constants.event_profile,
            localField: 'event_id',
            foreignField: 'EVENT_ID',
            as: 'events',
          },
        },
        {
          $match: {
            $or: !search
              ? [{ _id: { $ne: null } }]
              : [
                  { 'events.OPERATOR_ID': { $regex: search, $options: 'i' } },
                  { 'events.EVENT_ID': { $regex: search, $options: 'i' } },
                  { 'events.UAS_ID': { $in: [...search.split(',')] } },
                  { 'events.DEVICE_ID': { $regex: search, $options: 'i' } },
                ],
          },
        },
        { $sort: !sort ? { 'timestamp': -1 } : sortObj },
        {
          $project: {
            _id: 0,
          },
        },
        { $skip: skip },
        { $limit: limit },
      ])
      .exec();

    return events;
  }

  /**
   * Find event counts by alert zones (legacy method for compatibility)
   */
  async findEventCountsGroupedByAlertZones(orgId: string, search?: string, startDate?: Date, endDate?: Date, alertZones?: string[]) {
    this.logger.log(
      `Filters: startDate ${startDate?.toISOString()} EndDate: ${endDate?.toISOString()} orgId: ${orgId} alertZones: ${alertZones}`
    );

    const realOrgId = await this.organizationService.findByAuth0Id(orgId);
    const events = await this.notificationModel
      .aggregate([
        {
          $match: {
            org_id: realOrgId._id,
            'timestamp': {
              $gte: startDate,
              $lte: endDate,
            }
          },
        },
        {
          $lookup: {
            from: Constants.event_profile,
            localField: 'event_id',
            foreignField: 'EVENT_ID',
            as: 'events',
          },
        },
        {
          $match: {
            $or: !search
              ? [{ _id: { $ne: null } }]
              : [
                  { 'events.OPERATOR_ID': { $regex: search, $options: 'i' } },
                  { 'events.EVENT_ID': { $regex: search, $options: 'i' } },
                  { 'events.UAS_ID': { $in: [...search.split(',')]  } },
                  { 'events.DEVICE_ID': { $regex: search, $options: 'i' } },
                  { 'alertZone.name': { $regex: search, $options: 'i' } },
                  { 'alertZone._id': { $in: [...search.split(',')] } },
                ],
            $and: [{
              'alertZone._id': { $in: alertZones }
            }]
          },
        },
        {
          $group: {
            _id: {
              zoneId: '$alertZone._id',
              zoneName: '$alertZone.name',
            },
            count: {
              $sum: 1,
            },
          },
        },
        {
          $addFields: {
            zoneId: '$_id.zoneId',
            zoneName: '$_id.zoneName',
          },
        },
        {
          $project: {
            _id: 0,
          },
        },
      ])
      .exec();

    return events;
  }

  /**
   * Find one event with its history (enhanced version)
   */
  async findOne(eventId: string, historySize: number = 0): Promise<EventProfile> {
    // Get the event profile
    const eventProfile = await this.eventProfileService.findOne(eventId);

    // Get event history if requested
    const eventHistory = await this.eventHistoryService.findByEventId(eventId, historySize);
    eventProfile.PROCESSED_ITEMS = eventHistory;

    return eventProfile;
  }

  /**
   * Get event profile by ID
   */
  async getEventProfile(eventId: string): Promise<EventProfile> {
    return this.eventProfileService.findOne(eventId);
  }

  /**
   * Get event history by event ID
   */
  async getEventHistory(eventId: string, limit?: number): Promise<EventHistory[]> {
    return this.eventHistoryService.findByEventId(eventId, limit);
  }

  /**
   * Get events by device ID
   */
  async getEventsByDeviceId(deviceId: string): Promise<{
    profiles: EventProfile[];
    history: EventHistory[];
  }> {
    const [profiles, history] = await Promise.all([
      this.eventProfileService.findByDeviceId(deviceId),
      this.eventHistoryService.findByDeviceId(deviceId)
    ]);

    return { profiles, history };
  }

  /**
   * Get events by date range
   */
  async getEventsByDateRange(
    startDate: Date, 
    endDate: Date, 
    filters?: any
  ): Promise<{
    profiles: EventProfile[];
    history: EventHistory[];
  }> {
    const [profiles, history] = await Promise.all([
      this.eventProfileService.findByDateRange(startDate, endDate, filters),
      this.eventHistoryService.findByDateRange(startDate, endDate, filters)
    ]);

    return { profiles, history };
  }

  /**
   * Create event profile
   */
  async createEventProfile(eventProfileData: Partial<EventProfile>, userId?: string): Promise<EventProfile> {
    return this.eventProfileService.create(eventProfileData, userId);
  }

  /**
   * Create event history entry
   */
  async createEventHistory(eventHistoryData: Partial<EventHistory>): Promise<EventHistory> {
    return this.eventHistoryService.create(eventHistoryData);
  }

  /**
   * Create multiple event history entries
   */
  async createEventHistoryBatch(eventHistoryData: Partial<EventHistory>[]): Promise<EventHistory[]> {
    return this.eventHistoryService.createMany(eventHistoryData);
  }

  /**
   * Update event profile
   */
  async updateEventProfile(eventId: string, eventProfileData: Partial<EventProfile>, userId?: string): Promise<EventProfile> {
    return this.eventProfileService.update(eventId, eventProfileData, userId);
  }

  /**
   * Delete event profile
   */
  async deleteEventProfile(eventId: string, userId?: string): Promise<{ message: string }> {
    return this.eventProfileService.delete(eventId, userId);
  }

  /**
   * Get comprehensive event statistics
   */
  async getEventStatistics(filters?: any): Promise<any> {
    const [profileStats, historyStats] = await Promise.all([
      this.eventProfileService.getEventStats(filters),
      this.eventHistoryService.getEventHistoryStats()
    ]);

    return {
      profiles: profileStats,
      history: historyStats,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get latest event history for an event
   */
  async getLatestEventHistory(eventId: string): Promise<EventHistory | null> {
    return this.eventHistoryService.getLatestByEventId(eventId);
  }

  /**
   * Search events by location
   */
  async searchEventsByLocation(lat: number, lon: number, radiusKm: number = 1): Promise<EventHistory[]> {
    return this.eventHistoryService.findByLocation(lat, lon, radiusKm);
  }
}
