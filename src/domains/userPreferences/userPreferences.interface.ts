import { Document, Schema } from 'mongoose';

interface DeliveryPreferences {
  sms: boolean;
  email: boolean;
}

export interface UserPreferences extends Document {
  _id: string;
  user_id: Schema.Types.ObjectId;
  // Map Preferences
  showSatelliteViewByDefault: boolean;
  showAlertZones: boolean;
  showAllDroneTraffic: boolean;
  loadInitialPageOnRegionView: boolean;
  // Alert Preferences
  alerts: Map<string, boolean>;
  // Delivery Preferences
  delivery: Map<string, DeliveryPreferences>;
}
