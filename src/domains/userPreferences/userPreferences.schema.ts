import { Schema } from 'mongoose';

const DeliveryPreferencesSchema = new Schema(
  {
    sms: { type: Boolean, default: false },
    email: { type: Boolean, default: true },
  },
  { _id: false },
);

const UserPreferencesSchema = new Schema({
  user_id: Schema.Types.ObjectId,
  // Map Preferences
  showSatelliteViewByDefault: Boolean,
  showAlertZones: Boolean,
  showAllDroneTraffic: Boolean,
  loadInitialPageOnRegionView: Boolean,
  // Alert Preferences
  alerts: {
    type: Map,
    of: Boolean,
    default: {},
  },
  // Delivery Preferences
  delivery: {
    type: Map,
    of: DeliveryPreferencesSchema,
    default: {},
  },
});

export default UserPreferencesSchema;
